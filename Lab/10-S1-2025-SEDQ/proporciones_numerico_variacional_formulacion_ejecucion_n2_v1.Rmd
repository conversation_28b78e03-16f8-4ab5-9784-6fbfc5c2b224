f---
output:
  html_document:
    df_print: paged
    mathjax: true
  pdf_document: 
    latex_engine: xelatex
    keep_tex: true
  word_document: default
header-includes:
- \usepackage[spanish]{babel}
- \usepackage{amsmath}
- \usepackage{fontspec}
- \usepackage{unicode-math}
- \usepackage{graphicx}
- \usepackage{adjustbox}
- \usepackage{tikz}
- \usepackage{pgfplots}
- \usetikzlibrary{3d,babel}
icfes:
  competencia: formulacion_ejecucion
  nivel_dificultad: 2
  contenido:
    categoria: algebra_calculo
    tipo: generico
  contexto: comunitario
  eje_axial: eje2
  componente: numerico_variacional
---

```{r inicio, include=FALSE}
# Librerías esenciales
library(exams)
library(ggplot2)
library(knitr)
library(reticulate)
library(testthat)
library(data.table)
library(readxl)
library(datasets)

# Configurar Python si es necesario
use_python("/usr/bin/python3", required = TRUE)

# Configuración global
typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE, 
  message = FALSE,
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  echo = FALSE,
  results = "hide"
)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

```{r data_generation, echo=FALSE, results="hide"}
# Función principal de generación de datos
generar_datos <- function() {
  # Contextos posibles
  contextos <- list(
    list(entidad = "Ministerio de Transporte", elemento = "vehículos", condicion = "asegurados"),
    list(entidad = "DANE", elemento = "hogares", condicion = "con internet"),
    list(entidad = "Ministerio de Educación", elemento = "estudiantes", condicion = "becados"),
    list(entidad = "Superintendencia Financiera", elemento = "empresas", condicion = "certificadas"),
    list(entidad = "ICBF", elemento = "familias", condicion = "beneficiarias"),
    list(entidad = "Ministerio de Salud", elemento = "centros médicos", condicion = "acreditados")
  )
  
  # Seleccionar contexto aleatorio
  contexto <- sample(contextos, 1)[[1]]
  
  # Fracciones posibles (numerador menor que denominador)
  fracciones <- list(
    list(num = 1, den = 3), list(num = 2, den = 5), list(num = 3, den = 7),
    list(num = 4, den = 9), list(num = 2, den = 7), list(num = 3, den = 8),
    list(num = 1, den = 4), list(num = 3, den = 5), list(num = 5, den = 8),
    list(num = 2, den = 9), list(num = 4, den = 7), list(num = 1, den = 5)
  )
  
  fraccion <- sample(fracciones, 1)[[1]]
  
  # Total de elementos (entre 500,000 y 5,000,000)
  total <- sample(seq(500000, 5000000, 100000), 1)
  
  # Calcular cantidad que cumple la condición
  cantidad_condicion <- total * fraccion$num / fraccion$den
  
  # Generar distractores
  distractor_doble <- total * 2
  distractor_promedio <- total / 2
  distractor_porcentaje <- (fraccion$num / fraccion$den) * 100
  
  return(list(
    entidad = contexto$entidad,
    elemento = contexto$elemento,
    condicion = contexto$condicion,
    total = total,
    numerador = fraccion$num,
    denominador = fraccion$den,
    cantidad_condicion = cantidad_condicion,
    distractor_doble = distractor_doble,
    distractor_promedio = distractor_promedio,
    distractor_porcentaje = distractor_porcentaje
  ))
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales para facilitar uso
entidad <- datos$entidad
elemento <- datos$elemento
condicion <- datos$condicion
total <- datos$total
numerador <- datos$numerador
denominador <- datos$denominador
cantidad_condicion <- datos$cantidad_condicion
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba obligatoria de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }
  
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))
})
```

Question
========

Según el `r entidad`, en el país solo `r numerador` de cada `r denominador` `r elemento` están `r condicion`. Si el total de `r elemento` matriculados es `r format(total, big.mark = ".", decimal.mark = ",")`, al realizar la operación `r format(total, big.mark = ".", decimal.mark = ",")` por `r numerador`/`r denominador` se calcularía:

Answerlist
----------
- el doble de `r elemento` matriculados.
- la cantidad de `r elemento` `r condicion`.
- el promedio de `r elemento` matriculados.
- el porcentaje de `r elemento` `r condicion`.

Solution
========

Para resolver este problema, debemos analizar qué representa la operación matemática `r format(total, big.mark = ".", decimal.mark = ",")` × `r numerador`/`r denominador`.

**Análisis del problema:**

- Total de `r elemento` matriculados: `r format(total, big.mark = ".", decimal.mark = ",")`
- Proporción de `r elemento` `r condicion`: `r numerador` de cada `r denominador`
- Operación a analizar: `r format(total, big.mark = ".", decimal.mark = ",")` × `r numerador`/`r denominador`

**Interpretación matemática:**

Cuando multiplicamos el total por una fracción, estamos calculando qué parte del total representa esa fracción.

En este caso:

- `r format(total, big.mark = ".", decimal.mark = ",")` × `r numerador`/`r denominador` = `r format(cantidad_condicion, big.mark = ".", decimal.mark = ",")`

Este resultado representa la **cantidad de `r elemento` `r condicion`**.

**Verificación de las opciones:**

- **Opción A:** El doble sería `r format(total * 2, big.mark = ".", decimal.mark = ",")` 
- **Opción B:** La cantidad de `r elemento` `r condicion` es `r format(cantidad_condicion, big.mark = ".", decimal.mark = ",")` 
- **Opción C:** El promedio sería `r format(total / 2, big.mark = ".", decimal.mark = ",")` 
- **Opción D:** El porcentaje sería `r round((numerador/denominador)*100, 1)`% 

Answerlist
----------
- Falso. El doble de `r elemento` matriculados sería `r format(total * 2, big.mark = ".", decimal.mark = ",")`.
- Verdadero. La operación calcula exactamente la cantidad de `r elemento` `r condicion`.
- Falso. El promedio de `r elemento` matriculados sería `r format(total / 2, big.mark = ".", decimal.mark = ",")`.
- Falso. El porcentaje de `r elemento` `r condicion` es `r round((numerador/denominador)*100, 1)`%, no el resultado de la multiplicación.

Meta-information
================
exname: Interpretación de operaciones con fracciones en contexto
extype: schoice
exsolution: 0100
exshuffle: TRUE
exsection: Proporciones y Fracciones
